import React, { useState } from "react";
import { Accordion, AccordionItem } from "@heroui/accordion";
import { Button } from "@heroui/react";
import { FaPlane } from "react-icons/fa";

interface FlightCardProps {
  id?: string; // pass unique id when rendering a list
  airlineLogo: string;
  airlineName: string;
  departureTime: string;
  arrivalTime: string;
  departureDate: string;
  arrivalDate: string;
  departureCity: string;
  arrivalCity: string;
  departureAirport: string;
  arrivalAirport: string;
  duration: string;
  aircraft: string;
  price: string;
}

const DepartingFlight: React.FC<FlightCardProps> = ({
  id = "flight-1",
  airlineLogo,
  airlineName,
  departureTime,
  arrivalTime,
  departureDate,
  arrivalDate,
  departureCity,
  arrivalCity,
  departureAirport,
  arrivalAirport,
  duration,
  aircraft,
  price,
}) => {
  // selectedKeys is the controlled state for HeroUI Accordion
  const [selectedKeys, setSelectedKeys] = useState<React.Key[] | "all">([]);

  const handleSelectionChange = (keys: any) => {
    // Hero<PERSON> may give a Set<React.Key> or "all"
    if (keys === "all") setSelectedKeys("all");
    else if (keys instanceof Set) setSelectedKeys(Array.from(keys));
    else if (Array.isArray(keys)) setSelectedKeys(keys);
    else setSelectedKeys([]);
  };

  const isOpen =
    selectedKeys === "all" || (Array.isArray(selectedKeys) && selectedKeys.includes(id));

  return (
    <div className="w-full rounded-2xl border overflow-hidden">
      {/* controlled accordion so we can read open state */}
      <Accordion
        selectionMode="single"
        selectionBehavior="toggle"
        selectedKeys={selectedKeys}
        onSelectionChange={handleSelectionChange}
        className="rounded-2xl"
        variant="shadow"
      >
        <AccordionItem
          value={id}
          // trigger carries the bottom divider (so divider under header is always visible)
          classNames={{
            base: "rounded-2xl",
            trigger:
              "px-4 py-3 border-b border-gray-300 transition-colors data-[open=true]:bg-blue-50 data-[open=false]:bg-white",
            content: "bg-blue-50",
          }}
          title={
            /* HEADER: grid ensures the middle block is exactly centered */
            <div className="grid grid-cols-3 items-center w-full">
              {/* left: airline */}
              <div className="flex items-center gap-2">
                <img src={airlineLogo} alt={airlineName} className="h-6 w-6" />
                <span className="font-medium">{airlineName}</span>
              </div>

              {/* center: times + plane + non-stop (perfectly centered) */}
              <div className="flex items-center gap-6 justify-center">
                <div className="text-right">
                  <p className="text-lg font-semibold">{departureTime}</p>
                  <p className="text-xs text-gray-500">{departureDate}</p>
                </div>

                <div className="flex flex-col items-center">
                  <FaPlane className="text-gray-600 text-sm rotate-90 mb-1" />
                  <span className="text-xs text-gray-500">Non stop</span>
                  <div className="w-12 h-px bg-gray-400 my-1" />
                </div>

                <div className="text-left">
                  <p className="text-lg font-semibold">{arrivalTime}</p>
                  <p className="text-xs text-gray-500">{arrivalDate}</p>
                </div>
              </div>

              {/* right column intentionally empty to keep center alignment perfect */}
              <div />
            </div>
          }
        >
          {/* Expandable body (only this toggles) */}
          <div className="px-6 py-4 space-y-2 text-sm">
            <p>
              <span className="font-semibold">{departureTime}</span> – {departureAirport} (
              {departureCity})
            </p>
            <p className="text-gray-500">Travel time: {duration}</p>
            <p>
              <span className="font-semibold">{arrivalTime}</span> – {arrivalAirport} (
              {arrivalCity})
            </p>
            <p className="text-gray-500">
              {airlineName}, Economy, {aircraft}
            </p>
          </div>
        </AccordionItem>
      </Accordion>

      {/* Divider above footer (always visible) */}
      <div className="border-t border-gray-300" />

      {/* Footer (outside the Accordion so always visible). Color follows `isOpen`. */}
      <div
        className={`transition-colors ${isOpen ? "bg-blue-50" : "bg-white"}`}
      >
        <div className="flex items-center justify-between px-6 py-4">
          {/* price left */}
          <span className="text-lg font-bold">{price}</span>

          {/* button right */}
          <Button color="primary" className="rounded-xl">
            Select Flight
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DepartingFlight;
