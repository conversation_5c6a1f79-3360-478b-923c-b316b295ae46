import DepartingFlight from "@/components/globalComponents/Filter/DepartingFlight";

const ChangeFlight = ({ onBack }: { onBack?: () => void }) => {
  const flights = [
  {
    airlineLogo: "/logos/indigo.png",
    airlineName: "IndiGo",
    departureTime: "16:40",
    arrivalTime: "17:55",
    departureDate: "Thu, 25 Feb",
    arrivalDate: "Thu, 25 Feb",
    departureCity: "London",
    arrivalCity: "Munich",
    departureAirport: "London Airport (VTZ)",
    arrivalAirport: "Munich (HYD)",
    duration: "1 hr 15 min",
    aircraft: "Airbus A321neo, 6E 783",
    price: "$112",
  },
  {
    airlineLogo: "/logos/airindia.png",
    airlineName: "Air India",
    departureTime: "09:10",
    arrivalTime: "12:45",
    departureDate: "Fri, 26 Feb",
    arrivalDate: "Fri, 26 Feb",
    departureCity: "Paris",
    arrivalCity: "Berlin",
    departureAirport: "Paris CDG",
    arrivalAirport: "Berlin TXL",
    duration: "2 hr 35 min",
    aircraft: "Boeing 737 MAX",
    price: "$150",
  },
  // add more flights...
];
  return (
    <div className="bg-white rounded-xl p-4">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold">Change Flight</h2>
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            className="px-4 py-2 text-sm text-primary-200 hover:underline"
          >
            ← Back to Itinerary
          </button>
        )}
      </div>
      <div className="text-gray-600">
      <div className="space-y-4">
      {/* {flights.map((flight, idx) => (
        <DepartingFlight key={idx} {...flight} />
      ))} */}
      {flights.map((f, i) => (
  <DepartingFlight key={i} id={`flight-${i}`} {...f} />
))}
    </div>

      </div>
    </div>
  );
};

export default ChangeFlight;
